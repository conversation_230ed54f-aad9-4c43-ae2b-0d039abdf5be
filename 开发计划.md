# 写乎 Markdown 编辑器 - 开发计划

> 基于 `xiehu_full_blueprint.md` 蓝图文档制定的详细开发计划
> 创建时间：2025-07-28

## 项目概述

**项目名称**：写乎 Markdown 编辑器  
**项目定位**：Local-first × Git 工作流 × 所见即所得 × 公众号排版  
**目标用户**：技术写作者、开源维护者、公众号运营、小团队协作  
**开发周期**：12 周  

## 技术栈确认

### 前端技术栈
- **框架**：Next.js 20.x (React 19 + App Router)
- **编辑器**：CodeMirror 6
- **样式**：Tailwind CSS + shadcn/ui
- **Markdown 渲染**：markdown-it + emoji/katex/mermaid
- **实时协作**：Yjs + y-websocket/PartyKit
- **Git 集成**：isomorphic-git + lightning-fs
- **离线支持**：Workbox SW + IndexedDB
- **安全**：DOMPurify + ONNX Runtime Web
- **监控**：Sentry

### 开发工具
- **包管理**：pnpm Workspaces
- **测试**：vitest (覆盖率 ≥ 80%)
- **代码质量**：ESLint + Prettier + lint-staged
- **部署**：Vercel / Cloudflare Pages

## 开发阶段规划

### 第一阶段：MVP 基础功能 (第1-2周)

**目标**：建立项目基础架构，实现核心编辑功能

#### 任务清单
1. **项目初始化**
   - 创建 Next.js 项目结构
   - 配置 pnpm Workspaces
   - 设置 ESLint + Prettier + TypeScript
   - 配置 Tailwind CSS + shadcn/ui

2. **编辑器核心**
   - 集成 CodeMirror 6
   - 实现 Hybrid 编辑模式 (Markdown 源码 + 预览)
   - 基础 Decoration API 实现
   - 分栏布局 (编辑区 + 预览区)

3. **本地存储**
   - IndexedDB 数据层设计
   - 文档保存/加载功能
   - 基础文件管理

4. **Markdown 渲染**
   - markdown-it 集成
   - 基础插件支持 (emoji, katex, mermaid)
   - WebWorker 渲染优化

**验收标准**：
- 可以创建、编辑、保存 Markdown 文档
- 实时预览功能正常
- 支持基础 Markdown 语法
- 光标操作延迟 ≤ 5ms

### 第二阶段：PWA 离线支持 (第3-4周)

**目标**：实现 PWA 功能，支持离线编辑

#### 任务清单
1. **Service Worker 配置**
   - Workbox 集成
   - 缓存策略配置 (NetworkFirst for .md)
   - 离线页面处理

2. **PWA 功能**
   - Web App Manifest 配置
   - 安装提示 (beforeinstallprompt)
   - 离线状态检测

3. **离线编辑**
   - 离线数据同步机制
   - 冲突解决策略
   - 后台同步 (Background Sync)

**验收标准**：
- 应用可以安装为 PWA
- 离线状态下可以正常编辑
- PWA 覆盖率 ≥ 95%
- 断网刷新后仍可正常使用

### 第三阶段：Git 集成 (第5-6周)

**目标**：实现 Git 工作流，支持版本控制

#### 任务清单
1. **Git 基础功能**
   - isomorphic-git 集成
   - lightning-fs 文件系统
   - 基础 Git 操作 (init, add, commit)

2. **远程仓库集成**
   - GitHub OAuth 认证
   - push/pull 功能
   - 分支管理

3. **版本管理 UI**
   - 提交历史查看
   - 文件差异对比
   - 版本回滚功能

4. **Git 队列机制**
   - 离线提交队列
   - 自动同步机制
   - 冲突处理

**验收标准**：
- 支持 GitHub 仓库连接
- clone 10MB 仓库 ≤ 3s
- push 操作 ≤ 1s
- 离线提交可在重连后自动同步

### 第四阶段：公众号导出 (第7-8周)

**目标**：实现微信公众号富文本导出功能

#### 任务清单
1. **富文本转换器**
   - HTML 白名单转换
   - 微信支持的样式适配
   - 代码块特殊处理

2. **图片处理**
   - 图片压缩 (1080px)
   - 图床集成
   - 粘贴图片自动上传

3. **导出功能**
   - 一键复制富文本
   - 代码复制按钮
   - 预览效果

**验收标准**：
- 微信公众号粘贴 100% 正常
- 图片自动压缩和上传
- 代码块格式保持正确

### 第五阶段：实时协作 (第9-10周)

**目标**：实现多人实时协作功能

#### 任务清单
1. **CRDT 集成**
   - Yjs 集成
   - 实时同步机制
   - 冲突自动解决

2. **协作功能**
   - 多用户光标显示
   - 选区同步
   - @评论功能

3. **离线协作**
   - 离线编辑缓存
   - 重连后自动合并
   - 冲突提示

4. **后端服务**
   - WebSocket 服务 (PartyKit)
   - PostgreSQL 影子表
   - 用户管理

**验收标准**：
- 支持 50 人同时协作
- 操作延迟 ≤ 200ms
- 离线编辑可无冲突合并

### 第六阶段：插件系统与 Beta 发布 (第11-12周)

**目标**：完善插件生态，准备 Beta 发布

#### 任务清单
1. **插件系统**
   - ESModule 插件架构
   - JSON 清单规范
   - 命令/面板钩子
   - iframe 沙箱机制

2. **AI 功能**
   - ONNX Runtime Web 集成
   - 本地违规检测
   - 内容审核功能

3. **完善功能**
   - 目录导航优化
   - 搜索功能
   - 主题切换
   - 快捷键支持

4. **测试与优化**
   - 单元测试完善
   - 性能优化
   - 用户体验优化
   - 文档编写

**验收标准**：
- 插件热装卸不崩溃
- 测试覆盖率 ≥ 80%
- LCP ≤ 1s
- JS Error ≤ 0.1%

## 项目结构

```
xiehu-editor/
├── apps/
│   └── web/                 # Next.js 主应用
│       ├── app/            # App Router 页面
│       ├── components/     # React 组件
│       ├── lib/           # 工具函数
│       └── public/        # 静态资源
├── packages/
│   ├── editor-core/       # 编辑器核心
│   ├── renderer/          # Markdown 渲染器
│   ├── outline/           # 目录导航
│   ├── git-integration/   # Git 集成
│   ├── collaboration/     # 实时协作
│   ├── export/           # 导出功能
│   └── ui/               # UI 组件库
├── scripts/              # 构建脚本
├── docs/                # 项目文档
└── tests/               # 测试文件
```

## 开发规范

### 代码规范
- 使用 TypeScript 严格模式
- 遵循 ESLint + Prettier 配置
- 组件使用 React 19 新特性
- 优先使用 App Router

### Git 规范
- 分支命名：`feature/功能名`、`fix/问题描述`
- 提交格式：`feat(scope): description #issue`
- 使用 squash merge
- 每个 PR 必须通过 CI 检查

### 测试规范
- 单元测试覆盖率 ≥ 80%
- 集成测试覆盖核心流程
- E2E 测试覆盖用户关键路径
- 性能测试确保指标达标

## 风险评估与应对

### 技术风险
1. **CodeMirror 6 性能**：大文档编辑可能卡顿
   - 应对：虚拟滚动 + 分段渲染
2. **isomorphic-git 兼容性**：浏览器 Git 操作限制
   - 应对：降级方案 + 服务端代理
3. **实时协作复杂度**：CRDT 冲突处理
   - 应对：简化协作场景 + 手动解决

### 进度风险
1. **技术学习曲线**：新技术栈学习时间
   - 应对：提前技术调研 + 原型验证
2. **功能复杂度**：功能间耦合度高
   - 应对：模块化设计 + 渐进开发

## 下一步行动

1. **立即开始**：项目初始化和技术栈搭建
2. **第一周目标**：完成基础编辑器功能
3. **里程碑检查**：每两周进行功能验收
4. **持续优化**：根据测试反馈调整计划

---

*本开发计划将根据实际开发进度和用户反馈进行动态调整*
